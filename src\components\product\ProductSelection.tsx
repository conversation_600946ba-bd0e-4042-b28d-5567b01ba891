
import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useData } from '@/context/data';
import { ProductCategory } from '@/types';
import { toast } from 'sonner';
import { ProductSelectionContent } from './ProductSelectionContent';

const ProductSelection: React.FC = () => {
  const { visitId } = useParams<{ visitId: string }>();
  const navigate = useNavigate();
  const { 
    addProduct, 
    visits, 
    getDoorsByVisit,
    getHouseById,
    getAddressById
  } = useData();

  const [selectedDoors, setSelectedDoors] = useState<string[]>([]);
  const [products, setProducts] = useState<{category: ProductCategory; type: string; quantity: number}[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const visit = visits.find(v => v.id === visitId);
  if (!visit) {
    console.log('Visit nicht gefunden:', visitId);
    navigate('/');
    return null;
  }

  const doors = getDoorsByVisit(visitId!);
  const salesDoors = doors.filter(door => door.status === 'Angetroffen → Sale');
  
  console.log('Doors für Visit:', doors);
  console.log('Sales Doors:', salesDoors);
  console.log('Selected Doors:', selectedDoors);
  
  // Auto-select single door for EFH or no auto-selection for MFH
  useEffect(() => {
    if (salesDoors.length === 1 && selectedDoors.length === 0) {
      console.log('Auto-selecting single sales door:', salesDoors[0].id);
      setSelectedDoors([salesDoors[0].id]);
    }
  }, [salesDoors, selectedDoors.length]);

  const house = getHouseById(visit.houseId);
  if (!house) {
    navigate('/');
    return null;
  }

  const address = getAddressById(house.addressId);
  if (!address) {
    navigate('/');
    return null;
  }

  const addProductItem = (category: ProductCategory) => {
    const defaultOption = {
      category,
      type: category === 'KIP' ? 'KIP' : 
            category === 'TV' ? 'sky' : 'contract',
      quantity: 1
    };
    setProducts([...products, defaultOption]);
    console.log('Product hinzugefügt:', defaultOption);
  };

  const removeProductItem = (index: number) => {
    const newProducts = [...products];
    newProducts.splice(index, 1);
    setProducts(newProducts);
    console.log('Product entfernt, neue Liste:', newProducts);
  };

  const updateProductType = (index: number, type: string) => {
    const newProducts = [...products];
    newProducts[index].type = type;
    setProducts(newProducts);
  };

  const updateProductQuantity = (index: number, quantity: number) => {
    if (quantity < 1) return;
    
    const newProducts = [...products];
    newProducts[index].quantity = quantity;
    setProducts(newProducts);
  };

  const handleDoorToggle = (doorId: string) => {
    setSelectedDoors(prev => {
      if (prev.includes(doorId)) {
        return prev.filter(id => id !== doorId);
      } else {
        return [...prev, doorId];
      }
    });
  };

  const handleSubmit = async () => {
    if (selectedDoors.length === 0) {
      toast.error('Bitte wählen Sie mindestens eine Tür aus');
      return;
    }

    console.log('Submitting mit selectedDoors:', selectedDoors, 'und products:', products);
    setIsSubmitting(true);

    try {
      let totalProductsSaved = 0;
      
      if (products.length > 0) {
        for (const doorId of selectedDoors) {
          for (const product of products) {
            addProduct({
              doorId: doorId,
              category: product.category,
              type: product.type,
              quantity: product.quantity,
            });
            totalProductsSaved++;
          }
        }
        toast.success(`${totalProductsSaved} Produkte für ${selectedDoors.length} Tür(en) erfolgreich gespeichert`);
      } else {
        toast.success(`Besuch für ${selectedDoors.length} Tür(en) ohne Produkte gespeichert`);
      }

      navigate('/daily-view');
    } catch (error) {
      console.error(error);
      toast.error('Fehler beim Speichern der Produkte');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  return (
    <ProductSelectionContent
      address={address}
      house={house}
      salesDoors={salesDoors}
      selectedDoors={selectedDoors}
      products={products}
      isSubmitting={isSubmitting}
      onDoorToggle={handleDoorToggle}
      onAddProduct={addProductItem}
      onRemoveProduct={removeProductItem}
      onUpdateProductType={updateProductType}
      onUpdateProductQuantity={updateProductQuantity}
      onSubmit={handleSubmit}
      onBack={handleBack}
    />
  );
};

export default ProductSelection;
