
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AddressHeader } from './AddressHeader';
import { StatusButtons } from './StatusButtons';
import { Clock, Calendar } from 'lucide-react';
import { Address, House, Visit, Door, VisitStatus } from '@/types';

interface StatusSelectionStepProps {
  visitId: string;
  visit: Visit;
  address: Address;
  house: House;
  existingDoors: Door[];
  onStatusUpdate: (status: VisitStatus) => void;
  isUpdating: boolean;
}

export const StatusSelectionStep: React.FC<StatusSelectionStepProps> = ({
  visitId,
  visit,
  address,
  house,
  existingDoors,
  onStatusUpdate,
  isUpdating
}) => {
  const navigate = useNavigate();

  const getStatusColor = (status: VisitStatus) => {
    switch (status) {
      case 'Angetroffen → Termin':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Angetroffen → Kein Interesse':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'Angetroffen → Sale':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-red-100 text-red-800 border-red-200';
    }
  };

  return (
    <div className="space-y-6">
      <AddressHeader 
        address={address} 
        house={house} 
        step={2} 
        existingDoors={existingDoors}
      />
      
      <Card className="w-full bg-white/95 backdrop-blur-sm border-0 shadow-2xl rounded-3xl overflow-hidden animate-fade-in">
        <CardContent className="px-8 pt-6">
          {/* Current Status */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-semibold text-gray-700">Aktueller Status:</span>
              <Badge className={getStatusColor(visit.status)} variant="outline">
                {visit.status}
              </Badge>
            </div>

            {/* Show appointment info if exists */}
            {visit.status === 'Angetroffen → Termin' && visit.appointmentDate && visit.appointmentTime && (
              <div className="mb-3 p-3 bg-blue-50/60 rounded-xl border border-blue-200">
                <div className="flex items-center gap-2 text-sm text-blue-800">
                  <Calendar className="h-4 w-4" />
                  <span className="font-medium">
                    Termin: {format(new Date(visit.appointmentDate), 'dd.MM.yyyy')} um {visit.appointmentTime}
                  </span>
                </div>
              </div>
            )}
            
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <Clock className="h-4 w-4" />
              <span>{new Date(visit.timestamp).toLocaleString('de-DE')}</span>
            </div>
          </div>

          {/* Status Update Buttons */}
          <StatusButtons
            currentStatus={visit.status}
            onStatusUpdate={onStatusUpdate}
            isUpdating={isUpdating}
          />

          {/* Navigation Buttons */}
          <div className="mt-8 space-y-3">
            <Button
              onClick={() => navigate('/')}
              variant="outline"
              className="w-full h-12 text-lg font-semibold rounded-xl border-2 border-gray-300 hover:border-red-500 hover:text-red-600"
            >
              Zurück zur Startseite
            </Button>
            
            {visit.status === 'Angetroffen → Sale' && (
              <Button
                onClick={() => navigate(`/products/${visitId}`)}
                className="w-full h-12 text-lg font-semibold bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 rounded-xl text-white"
              >
                Produkte erfassen
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
