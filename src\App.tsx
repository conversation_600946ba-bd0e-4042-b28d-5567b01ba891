
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/context/auth/AuthContext';
import { DataProvider } from '@/context/data';
import { ThemeProvider } from '@/context/theme/ThemeProvider';
import { SettingsProvider } from '@/context/settings/SettingsProvider';
import { Toaster } from '@/components/ui/sonner';
import ProtectedRoute from '@/components/ProtectedRoute';

// Pages
import Index from '@/pages/Index';
import Login from '@/pages/Login';
import EFHPage from '@/pages/EFHPage';
import MFHPage from '@/pages/MFHPage';
import MapPage from '@/pages/MapPage';
import DailyViewPage from '@/pages/DailyViewPage';
import StatisticsPage from '@/pages/StatisticsPage';
import TeamsStatisticsPage from '@/pages/TeamsStatisticsPage';
import BeraterStatisticsPage from '@/pages/BeraterStatisticsPage';
import ProfilePage from '@/pages/ProfilePage';
import SettingsPage from '@/pages/SettingsPage';
import CalendarPage from '@/pages/CalendarPage';
import UserManagementPage from '@/pages/UserManagementPage';
import TeamManagementPage from '@/pages/TeamManagementPage';
import AreaManagementPage from '@/pages/AreaManagementPage';
import TeamOverviewPage from '@/pages/TeamOverviewPage';
import TeamsOverviewPage from '@/pages/TeamsOverviewPage';
import AreaOverviewPage from '@/pages/AreaOverviewPage';
import VisitStatusPage from '@/pages/VisitStatusPage';
import ProductSelectionPage from '@/pages/ProductSelectionPage';
import NotFound from '@/pages/NotFound';
import MFHManagerPage from '@/pages/MFHManagerPage';

import './App.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <SettingsProvider>
          <AuthProvider>
            <DataProvider>
              <Router>
                <div className="min-h-screen bg-background">
                  <Routes>
                    <Route path="/login" element={<Login />} />
                    <Route 
                      path="/" 
                      element={
                        <ProtectedRoute>
                          <Index />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/efh" 
                      element={
                        <ProtectedRoute>
                          <EFHPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/mfh" 
                      element={
                        <ProtectedRoute>
                          <MFHPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/mfh-manager" 
                      element={
                        <ProtectedRoute>
                          <MFHManagerPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/map" 
                      element={
                        <ProtectedRoute>
                          <MapPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/daily-view" 
                      element={
                        <ProtectedRoute>
                          <DailyViewPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/statistics" 
                      element={
                        <ProtectedRoute>
                          <StatisticsPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/teams-statistics" 
                      element={
                        <ProtectedRoute>
                          <TeamsStatisticsPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/berater-statistics" 
                      element={
                        <ProtectedRoute>
                          <BeraterStatisticsPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/profile" 
                      element={
                        <ProtectedRoute>
                          <ProfilePage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/settings" 
                      element={
                        <ProtectedRoute>
                          <SettingsPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/calendar" 
                      element={
                        <ProtectedRoute>
                          <CalendarPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/user-management" 
                      element={
                        <ProtectedRoute>
                          <UserManagementPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/team-management" 
                      element={
                        <ProtectedRoute>
                          <TeamManagementPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/area-management" 
                      element={
                        <ProtectedRoute>
                          <AreaManagementPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/team-overview" 
                      element={
                        <ProtectedRoute>
                          <TeamOverviewPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/teams-overview" 
                      element={
                        <ProtectedRoute>
                          <TeamsOverviewPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/area-overview" 
                      element={
                        <ProtectedRoute>
                          <AreaOverviewPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/visit-status" 
                      element={
                        <ProtectedRoute>
                          <VisitStatusPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route 
                      path="/product-selection" 
                      element={
                        <ProtectedRoute>
                          <ProductSelectionPage />
                        </ProtectedRoute>
                      } 
                    />
                    <Route path="/404" element={<NotFound />} />
                    <Route path="*" element={<Navigate to="/404" replace />} />
                  </Routes>
                </div>
                <Toaster />
              </Router>
            </DataProvider>
          </AuthProvider>
        </SettingsProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
