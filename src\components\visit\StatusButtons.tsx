
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { VisitStatus } from '@/types';
import { 
  CheckCircle, 
  XCircle, 
  Calendar, 
  UserX
} from 'lucide-react';

interface StatusButtonsProps {
  currentStatus: VisitStatus;
  onStatusUpdate: (status: VisitStatus) => void;
  isUpdating: boolean;
}

export const StatusButtons: React.FC<StatusButtonsProps> = ({
  currentStatus,
  onStatusUpdate,
  isUpdating
}) => {
  const statusOptions: { status: VisitStatus; label: string; icon: React.ReactNode; color: string }[] = [
    {
      status: 'N/A',
      label: 'Nicht angetroffen',
      icon: <UserX className="h-5 w-5" />,
      color: 'bg-red-500 hover:bg-red-600'
    },
    {
      status: 'Angetroffen → Termin',
      label: 'Termin vereinbart',
      icon: <Calendar className="h-5 w-5" />,
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      status: '<PERSON><PERSON><PERSON><PERSON><PERSON> → Kein Interesse',
      label: 'Kein Interesse',
      icon: <XCircle className="h-5 w-5" />,
      color: 'bg-gray-500 hover:bg-gray-600'
    },
    {
      status: 'Angetroffen → Sale',
      label: 'Verkauf abgeschlossen',
      icon: <CheckCircle className="h-5 w-5" />,
      color: 'bg-green-500 hover:bg-green-600'
    }
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">Besuchsstatus wählen:</h3>
      
      {statusOptions.map((option) => (
        <Button
          key={option.status}
          onClick={() => onStatusUpdate(option.status)}
          disabled={isUpdating || currentStatus === option.status}
          className={`w-full h-16 text-lg font-semibold rounded-xl transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl ${option.color} text-white`}
        >
          <div className="flex items-center gap-3">
            {option.icon}
            <span>{option.label}</span>
            {currentStatus === option.status && (
              <CheckCircle className="h-5 w-5 ml-auto" />
            )}
          </div>
        </Button>
      ))}
    </div>
  );
};
