
import React from 'react';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Door } from '@/types';

interface DoorSelectionProps {
  salesDoors: Door[];
  selectedDoors: string[];
  onDoorToggle: (doorId: string) => void;
}

export const DoorSelection: React.FC<DoorSelectionProps> = ({
  salesDoors,
  selectedDoors,
  onDoorToggle
}) => {
  const isMultiDoor = salesDoors.length > 1;

  if (!isMultiDoor) {
    return null;
  }

  return (
    <div className="mb-4 p-4 glass-card rounded-xl">
      <Label className="text-base font-semibold text-gray-800 mb-3 block">
        Türen auswählen ({selectedDoors.length} von {salesDoors.length} ausgewählt)
      </Label>
      <div className="space-y-2">
        {salesDoors.map(door => (
          <div key={door.id} className="flex items-center space-x-3 p-2 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
            <Checkbox 
              id={`door-${door.id}`}
              checked={selectedDoors.includes(door.id)}
              onCheckedChange={() => onDoorToggle(door.id)}
              className="h-4 w-4"
            />
            <Label 
              htmlFor={`door-${door.id}`} 
              className="text-sm font-medium text-gray-700 cursor-pointer flex-1"
            >
              {door.name} {door.floor ? `(${door.floor})` : ''}
            </Label>
          </div>
        ))}
      </div>
    </div>
  );
};
