
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/context/auth';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { Switch } from '@/components/ui/switch';
import { ChevronDown, User, Lock, Zap } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface LoginFormProps {
  email: string;
  setEmail: (email: string) => void;
  password: string;
  setPassword: (password: string) => void;
}

const LoginForm: React.FC<LoginFormProps> = ({ 
  email, 
  setEmail, 
  password, 
  setPassword 
}) => {
  const navigate = useNavigate();
  const { login, isLoading } = useAuth();
  const [showAccountSwitcher, setShowAccountSwitcher] = useState(false);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const success = await login(email, password);
      if (success) {
        navigate('/');
      }
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  const handleQuickLogin = async (role: string) => {
    let accountEmail = '';
    const accountPassword = 'test123';

    switch(role) {
      case 'berater':
        accountEmail = '<EMAIL>';
        break;
      case 'mentor':
        accountEmail = '<EMAIL>';
        break;
      case 'teamleiter':
        accountEmail = '<EMAIL>';
        break;
      case 'manager':
        accountEmail = '<EMAIL>';
        break;
      case 'admin':
        accountEmail = '<EMAIL>';
        break;
      default:
        accountEmail = '<EMAIL>';
    }

    setEmail(accountEmail);
    setPassword(accountPassword);

    try {
      const success = await login(accountEmail, accountPassword);
      if (success) {
        navigate('/');
      }
    } catch (error) {
      console.error('Quick login error:', error);
    }
  };

  const testAccounts = [
    { role: 'berater', label: 'Berater', icon: User, color: 'from-blue-500 to-blue-600' },
    { role: 'mentor', label: 'Mentor', icon: Zap, color: 'from-green-500 to-green-600' },
    { role: 'teamleiter', label: 'Teamleiter', icon: User, color: 'from-purple-500 to-purple-600' },
    { role: 'manager', label: 'Manager', icon: User, color: 'from-orange-500 to-orange-600' },
    { role: 'admin', label: 'Admin', icon: Lock, color: 'from-red-500 to-red-600' },
  ];

  return (
    <form onSubmit={handleLogin} className="space-y-6">
      {/* Test Account Switcher */}
      <Collapsible open={showAccountSwitcher} onOpenChange={setShowAccountSwitcher}>
        <CollapsibleTrigger asChild>
          <Button
            type="button"
            variant="outline"
            className="w-full justify-between text-sm text-gray-600 border-gray-200 hover:bg-gray-50 h-10"
          >
            <span>Test-Accounts</span>
            <ChevronDown className={`h-4 w-4 transition-transform ${showAccountSwitcher ? 'rotate-180' : ''}`} />
          </Button>
        </CollapsibleTrigger>
        
        <CollapsibleContent className="mt-3 animate-slide-in">
          <div className="p-3 bg-gray-50 rounded-xl border space-y-2">
            {/* First row: Berater + Mentor */}
            <div className="grid grid-cols-2 gap-2">
              {testAccounts.slice(0, 2).map((account) => {
                const IconComponent = account.icon;
                return (
                  <Button
                    key={account.role}
                    type="button"
                    variant="outline"
                    onClick={() => handleQuickLogin(account.role)}
                    className={`h-10 justify-center text-xs bg-gradient-to-r ${account.color} text-white border-0 hover:opacity-90 transition-all duration-200 hover:scale-105`}
                  >
                    <IconComponent className="h-3 w-3 mr-1" />
                    {account.label}
                  </Button>
                );
              })}
            </div>
            
            {/* Second row: Teamleiter + Manager */}
            <div className="grid grid-cols-2 gap-2">
              {testAccounts.slice(2, 4).map((account) => {
                const IconComponent = account.icon;
                return (
                  <Button
                    key={account.role}
                    type="button"
                    variant="outline"
                    onClick={() => handleQuickLogin(account.role)}
                    className={`h-10 justify-center text-xs bg-gradient-to-r ${account.color} text-white border-0 hover:opacity-90 transition-all duration-200 hover:scale-105`}
                  >
                    <IconComponent className="h-3 w-3 mr-1" />
                    {account.label}
                  </Button>
                );
              })}
            </div>
            
            {/* Third row: Admin centered */}
            <div className="flex justify-center">
              {testAccounts.slice(4, 5).map((account) => {
                const IconComponent = account.icon;
                return (
                  <Button
                    key={account.role}
                    type="button"
                    variant="outline"
                    onClick={() => handleQuickLogin(account.role)}
                    className={`h-10 w-24 justify-center text-xs bg-gradient-to-r ${account.color} text-white border-0 hover:opacity-90 transition-all duration-200 hover:scale-105`}
                  >
                    <IconComponent className="h-3 w-3 mr-1" />
                    {account.label}
                  </Button>
                );
              })}
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Email/Username Input */}
      <div className="space-y-3">
        <Label htmlFor="email" className="text-base font-semibold text-gray-700">
          E-Mail oder Name
        </Label>
        <div className="relative">
          <User className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <Input
            id="email"
            type="text"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            className="h-14 pl-12 text-lg bg-white border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-red-500 transition-all duration-200"
            placeholder="<EMAIL> oder Name"
          />
        </div>
      </div>

      {/* Password Input */}
      <div className="space-y-3">
        <Label htmlFor="password" className="text-base font-semibold text-gray-700">
          Passwort
        </Label>
        <div className="relative">
          <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <Input
            id="password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            className="h-14 pl-12 text-lg bg-white border-2 border-gray-200 rounded-xl focus:border-red-500 focus:ring-red-500 transition-all duration-200"
            placeholder="••••••••"
          />
        </div>
      </div>

      {/* Login Button */}
      <Button
        type="submit"
        className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 rounded-xl transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl"
        disabled={isLoading || !email || !password}
      >
        {isLoading ? (
          <div className="flex items-center">
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
            Anmeldung...
          </div>
        ) : (
          'Anmelden'
        )}
      </Button>
    </form>
  );
};

export default LoginForm;
