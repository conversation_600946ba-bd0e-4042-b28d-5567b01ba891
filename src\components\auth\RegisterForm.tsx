
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/context/auth';
import { toast } from 'sonner';

interface RegisterFormProps {
  name: string;
  setName: (name: string) => void;
  email: string;
  setEmail: (email: string) => void;
  password: string;
  setPassword: (password: string) => void;
  setActiveTab: (tab: "login" | "register") => void;
}

const RegisterForm: React.FC<RegisterFormProps> = ({ 
  name, 
  setName, 
  email, 
  setEmail, 
  password, 
  setPassword,
  setActiveTab 
}) => {
  const { register, isLoading } = useAuth();

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await register(name, email, password);
      toast.success('Registrierung erfolgreich');
      setActiveTab("login");
    } catch (error) {
      toast.error('Registrierung fehlgeschlagen');
    }
  };

  return (
    <form onSubmit={handleRegister} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Name</Label>
        <Input
          id="name"
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
          className="bg-white"
          placeholder="Max Mustermann"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="register-email">E-Mail</Label>
        <Input
          id="register-email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="bg-white"
          placeholder="<EMAIL>"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="register-password">Passwort</Label>
        <Input
          id="register-password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className="bg-white"
          placeholder="••••••••"
        />
      </div>
      <Button
        type="submit"
        className="w-full bg-red-600 hover:bg-red-700"
        disabled={isLoading}
      >
        Registrieren
      </Button>
    </form>
  );
};

export default RegisterForm;
