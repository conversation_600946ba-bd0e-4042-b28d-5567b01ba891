
import React from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardFooter } from '@/components/ui/card';
import { Address, House, Door, ProductCategory } from '@/types';
import { AddressInfo } from './AddressInfo';
import { InfoBanners } from './InfoBanners';
import { DoorSelection } from './DoorSelection';
import { ProductTabs } from './ProductTabs';
import { SaveButton } from './SaveButton';

interface ProductSelectionContentProps {
  address: Address;
  house: House;
  salesDoors: Door[];
  selectedDoors: string[];
  products: {category: ProductCategory; type: string; quantity: number}[];
  isSubmitting: boolean;
  onDoorToggle: (doorId: string) => void;
  onAddProduct: (category: ProductCategory) => void;
  onRemoveProduct: (index: number) => void;
  onUpdateProductType: (index: number, type: string) => void;
  onUpdateProductQuantity: (index: number, quantity: number) => void;
  onSubmit: () => void;
  onBack: () => void;
}

export const ProductSelectionContent: React.FC<ProductSelectionContentProps> = ({
  address,
  house,
  salesDoors,
  selectedDoors,
  products,
  isSubmitting,
  onDoorToggle,
  onAddProduct,
  onRemoveProduct,
  onUpdateProductType,
  onUpdateProductQuantity,
  onSubmit,
  onBack
}) => {
  const isMultiDoor = salesDoors.length > 1;
  const canSave = selectedDoors.length > 0 && !isSubmitting;

  return (
    <div className="p-4 md:p-6">
      <div className="max-w-4xl mx-auto">
        <Card className="glass-card border-0 shadow-xl rounded-2xl overflow-hidden">
          <CardHeader className="px-4 md:px-6 pb-2">
            <AddressInfo address={address} house={house} />
          </CardHeader>
          
          <CardContent className="px-4 md:px-6 pt-2">
            <InfoBanners
              isMultiDoor={isMultiDoor}
              salesDoors={salesDoors}
              selectedDoors={selectedDoors}
              canSave={canSave}
              isSubmitting={isSubmitting}
            />

            <DoorSelection
              salesDoors={salesDoors}
              selectedDoors={selectedDoors}
              onDoorToggle={onDoorToggle}
            />

            <ProductTabs
              products={products}
              onAddProduct={onAddProduct}
              onRemoveProduct={onRemoveProduct}
              onUpdateProductType={onUpdateProductType}
              onUpdateProductQuantity={onUpdateProductQuantity}
            />
          </CardContent>
          
          <CardFooter className="px-4 md:px-6 pb-6">
            <SaveButton
              canSave={canSave}
              isSubmitting={isSubmitting}
              selectedDoorsCount={selectedDoors.length}
              productsCount={products.length}
              onSubmit={onSubmit}
              onBack={onBack}
            />
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};
