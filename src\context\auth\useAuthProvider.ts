import { useState, useEffect } from "react";
import { User } from "../../types";
import { mockUsers } from "./mockUsers";
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'sonner';

export const useAuthProvider = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [users, setUsers] = useState<User[]>([]);

  // Load users from localStorage on initial load
  useEffect(() => {
    console.log("Initial load, checking for stored users and current user...");
    
    // First load the mock users to ensure we always have these accounts
    setUsers(mockUsers);
    localStorage.setItem("users", JSON.stringify(mockUsers));
    
    // Then check if user is stored in localStorage
    const storedUser = localStorage.getItem("currentUser");
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
    setIsLoading(false);
  }, []);

  const login = async (emailOrName: string, password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      console.log("Attempting to log in with:", emailOrName, "password length:", password.length);
      console.log("Available users:", users.map(u => ({ email: u.email, name: u.name })));
      
      // Find user with matching email/name and password
      const foundUser = users.find(
        (u) => (u.email === emailOrName || u.name === emailOrName) && u.password === password
      );

      if (foundUser) {
        console.log("User found:", foundUser.name, foundUser.role);
        const { password: _, ...userWithoutPassword } = foundUser;
        setUser(userWithoutPassword as User);
        localStorage.setItem("currentUser", JSON.stringify(userWithoutPassword));
        toast.success(`Willkommen zurück, ${foundUser.name}!`);
        return true;
      } else {
        console.log("User not found or password incorrect");
        setError("Ungültige Anmeldeinformationen");
        toast.error("Anmeldung fehlgeschlagen. Ungültiger Benutzername oder Passwort.");
        return false;
      }
    } catch (err) {
      console.error("Login error:", err);
      setError("Anmeldefehler. Bitte versuchen Sie es erneut.");
      toast.error("Ein Fehler ist aufgetreten.");
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (name: string, email: string, password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Check if user with this email already exists
      const existingUser = users.find((u) => u.email === email);
      if (existingUser) {
        setError("E-Mail-Adresse wird bereits verwendet");
        toast.error("Diese E-Mail-Adresse existiert bereits.");
        setIsLoading(false);
        return;
      }

      // Create new user
      const newUser: User = {
        id: uuidv4(),
        name,
        email,
        role: "berater", // Default role for new registrations
        teamId: "team1", // Default team
        password, // In a real app, you would hash this
      };

      setUsers((prev) => [...prev, newUser]);
      
      // Automatically log in the new user
      const { password: _, ...userWithoutPassword } = newUser;
      setUser(userWithoutPassword as User);
      localStorage.setItem("currentUser", JSON.stringify(userWithoutPassword));
      
      toast.success("Registrierung erfolgreich!");
    } catch (err) {
      setError("Registrierungsfehler. Bitte versuchen Sie es erneut.");
      toast.error("Ein Fehler ist aufgetreten.");
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setUser(null);
    localStorage.removeItem("currentUser");
    toast.info("Abmeldung erfolgreich.");
    
    // Return resolved promise
    return Promise.resolve();
  };

  const updateUser = (updatedUser: User) => {
    setUsers(users.map(u => u.id === updatedUser.id ? updatedUser : u));
    
    // If the current user is being updated, update the local state and storage
    if (user && user.id === updatedUser.id) {
      setUser(updatedUser);
      localStorage.setItem("currentUser", JSON.stringify(updatedUser));
    }
  };

  const createUser = (newUser: Omit<User, "id">) => {
    const userWithId = {
      ...newUser,
      id: uuidv4(),
    };
    
    setUsers([...users, userWithId]);
  };

  // Google login implementation
  const loginWithGoogle = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate API call delay for Google auth
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // For demonstration purposes, create a Google user
      const googleUser: User = {
        id: uuidv4(),
        name: "Google User",
        email: `user_${Math.floor(Math.random() * 10000)}@gmail.com`,
        role: "berater", // Default role
        teamId: "team1", // Default team
      };

      // Add user to the users array if not already present
      if (!users.some(u => u.email === googleUser.email)) {
        setUsers(prev => [...prev, googleUser]);
      }

      setUser(googleUser);
      localStorage.setItem("currentUser", JSON.stringify(googleUser));
      toast.success(`Willkommen, ${googleUser.name}!`);
    } catch (err) {
      setError("Google-Anmeldung fehlgeschlagen");
      toast.error("Ein Fehler ist bei der Google-Anmeldung aufgetreten.");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    user,
    login,
    register,
    logout,
    isLoading,
    error,
    users,
    updateUser,
    createUser,
    loginWithGoogle,
  };
};
