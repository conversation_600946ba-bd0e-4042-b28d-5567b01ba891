
import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

interface SaveButtonProps {
  canSave: boolean;
  isSubmitting: boolean;
  selectedDoorsCount: number;
  productsCount: number;
  onSubmit: () => void;
  onBack: () => void;
}

export const SaveButton: React.FC<SaveButtonProps> = ({
  canSave,
  isSubmitting,
  selectedDoorsCount,
  productsCount,
  onSubmit,
  onBack
}) => {
  return (
    <div className="w-full space-y-3">
      <Button 
        onClick={onSubmit}
        className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 rounded-xl text-white shadow-lg hover:shadow-xl transition-all duration-200" 
        disabled={!canSave}
      >
        {isSubmitting ? 'Speichern...' : 
         productsCount > 0 ? 
           `${productsCount} Produkte für ${selectedDoorsCount} Tür(en) speichern` : 
           `Für ${selectedDoorsCount} Tür(en) ohne Produkte speichern`}
      </Button>
      
      <Button
        onClick={onBack}
        variant="outline"
        className="w-full h-12 text-base font-semibold rounded-xl border-2 border-gray-300 hover:border-red-500 hover:text-red-600 transition-all duration-200"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Zurück
      </Button>
    </div>
  );
};
