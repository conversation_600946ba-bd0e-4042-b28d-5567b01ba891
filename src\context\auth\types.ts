
import { User as AppUser, UserRole } from "../../types";

// Auth specific types
export interface AuthContextType {
  user: AppUser | null;
  login: (emailOrName: string, password: string) => Promise<boolean>;
  register: (name: string, email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  isLoading: boolean;
  error: string | null;
  users: AppUser[]; // For admin management
  updateUser: (user: AppUser) => void;
  createUser: (user: Omit<AppUser, "id">) => void;
  loginWithGoogle: () => Promise<void>;
}
