
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useData } from '@/context/data';
import { VisitStatus } from '@/types';
import { toast } from 'sonner';
import { DoorCreationStep } from './DoorCreationStep';
import { StatusSelectionStep } from './StatusSelectionStep';
import { AppointmentDialog } from './AppointmentDialog';
import { format } from 'date-fns';

interface EFHVisitTrackerProps {
  visitId: string;
}

const EFHVisitTracker: React.FC<EFHVisitTrackerProps> = ({ visitId }) => {
  const navigate = useNavigate();
  const { getVisit, getHouseById, getAddressById, updateVisitStatus, addDoor, getDoorsByVisit, updateDoorStatus } = useData();
  const [isUpdating, setIsUpdating] = useState(false);
  const [isCreatingDoor, setIsCreatingDoor] = useState(false);
  const [showAppointmentDialog, setShowAppointmentDialog] = useState(false);
  
  const visit = getVisit(visitId);
  const house = visit ? getHouseById(visit.houseId) : null;
  const address = house ? getAddressById(house.addressId) : null;
  const existingDoors = getDoorsByVisit(visitId);

  if (!visit || !house || !address) {
    return (
      <div className="w-full bg-white/95 backdrop-blur-sm border-0 shadow-2xl rounded-3xl p-8 text-center">
        <p className="text-gray-500">Besuch nicht gefunden</p>
      </div>
    );
  }

  const handleCreateDoor = async (doorName: string) => {
    setIsCreatingDoor(true);
    try {
      addDoor({
        visitId: visitId,
        name: doorName,
        status: 'N/A'
      });
      toast.success('Tür erfolgreich erstellt');
    } catch (error) {
      toast.error('Fehler beim Erstellen der Tür');
    } finally {
      setIsCreatingDoor(false);
    }
  };

  const handleStatusUpdate = async (status: VisitStatus) => {
    if (status === 'Angetroffen → Termin') {
      setShowAppointmentDialog(true);
      return;
    }

    setIsUpdating(true);
    try {
      updateVisitStatus(visitId, status);
      
      // Update door status if it exists
      if (existingDoors.length > 0) {
        updateDoorStatus(existingDoors[0].id, status);
      }
      
      toast.success('Status erfolgreich aktualisiert');
      
      // Navigate to products page immediately if sale
      if (status === 'Angetroffen → Sale') {
        navigate(`/products/${visitId}`);
      }
    } catch (error) {
      toast.error('Fehler beim Aktualisieren des Status');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleAppointmentConfirm = async (date: string, time: string) => {
    setIsUpdating(true);
    try {
      updateVisitStatus(visitId, 'Angetroffen → Termin', date, time);
      
      // Update door status if it exists
      if (existingDoors.length > 0) {
        updateDoorStatus(existingDoors[0].id, 'Angetroffen → Termin', date, time);
      }
      
      toast.success(`Termin erfolgreich vereinbart für ${format(new Date(date), 'dd.MM.yyyy')} um ${time}`);
    } catch (error) {
      toast.error('Fehler beim Speichern des Termins');
    } finally {
      setIsUpdating(false);
    }
  };

  // Step 1: Show door creation if no door exists
  if (existingDoors.length === 0) {
    return (
      <DoorCreationStep
        visitId={visitId}
        address={address}
        house={house}
        onCreateDoor={handleCreateDoor}
        isCreating={isCreatingDoor}
      />
    );
  }

  // Step 2: Show status selection when door exists
  return (
    <>
      <StatusSelectionStep
        visitId={visitId}
        visit={visit}
        address={address}
        house={house}
        existingDoors={existingDoors}
        onStatusUpdate={handleStatusUpdate}
        isUpdating={isUpdating}
      />

      {/* Appointment Dialog */}
      <AppointmentDialog
        open={showAppointmentDialog}
        onClose={() => setShowAppointmentDialog(false)}
        onConfirm={handleAppointmentConfirm}
      />
    </>
  );
};

export default EFHVisitTracker;
